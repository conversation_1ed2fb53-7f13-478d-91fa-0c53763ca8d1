import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial
from typing import Optional, Callable
from timm.models.layers import DropPath, trunc_normal_


class Heat2D_ViT(nn.Module):
    """
    Heat2D模块，适配ViT架构，用热传导方程替代自注意力机制

    输入格式: [B, H*W, C] (序列格式，B=批次大小，H*W=patch数量，C=特征维度)
    输出格式: [B, H*W, C] (保持相同的序列格式)

    基于热传导方程的解析解:
    偏微分方程: du/dt - k(d²u/dx² + d²u/dy²) = 0
    解析解: u(x,y,t) = Σ A_nm * cos(nπx/a) * cos(mπy/b) * exp(-[(nπ/a)² + (mπ/b)²]kt)

    核心思想:
    1. 使用DCT将空间域信号转换到频域
    2. 在频域应用热传导衰减（低频保留，高频衰减）
    3. 使用IDCT转换回空间域
    """

    def __init__(self, dim=768, hidden_dim=None, resolution=14, infer_mode=False, **kwargs):
        """
        初始化Heat2D模块

        Args:
            dim (int): 输入特征维度，默认768
            hidden_dim (int): 隐藏层维度，默认等于dim
            resolution (int): 空间分辨率（假设为正方形），默认14（对应14x14的patch grid）
            infer_mode (bool): 推理模式，预计算衰减权重以加速推理
        """
        super().__init__()
        hidden_dim = hidden_dim or dim
        self.dim = dim
        self.hidden_dim = hidden_dim
        self.resolution = resolution
        self.infer_mode = infer_mode

        # 线性投影层
        self.norm = nn.LayerNorm(dim)  # 输入归一化: [B, H, W, C] -> [B, H, W, C]
        self.linear = nn.Linear(dim, 2 * hidden_dim, bias=True)  # 特征扩展: [B, H, W, C] -> [B, H, W, 2*hidden_dim]
        self.out_norm = nn.LayerNorm(hidden_dim)  # 输出归一化: [B, H, W, hidden_dim] -> [B, H, W, hidden_dim]
        self.out_linear = nn.Linear(hidden_dim, dim, bias=True)  # 特征压缩: [B, H, W, hidden_dim] -> [B, H, W, C]

        # 频率嵌入变换网络，用于生成自适应的衰减系数
        self.to_k = nn.Sequential(
            nn.Linear(dim, hidden_dim, bias=True),  # [H, W, C] -> [H, W, hidden_dim]
            nn.ReLU(),
        )

    def infer_init_heat2d(self, freq_embed):
        """
        推理模式初始化，预计算衰减权重以加速推理

        Args:
            freq_embed: [H, W, C] 频率嵌入张量
        """
        weight_exp = self.get_decay_map((self.resolution, self.resolution), device=freq_embed.device)
        # 预计算自适应衰减权重: [H, W, hidden_dim]
        self.k_exp = nn.Parameter(
            torch.pow(weight_exp[:, :, None], self.to_k(freq_embed)),
            requires_grad=False
        )
        del self.to_k  # 删除不再需要的网络以节省内存

    @staticmethod
    def get_cos_map(N=14, device=torch.device("cpu"), dtype=torch.float):
        """
        生成DCT/IDCT变换矩阵

        Args:
            N (int): 变换大小
            device: 计算设备
            dtype: 数据类型

        Returns:
            torch.Tensor: [N, N] DCT变换矩阵

        数学原理: DCT基函数 cos((x + 0.5) / N * n * π)
        """
        # 生成DCT变换矩阵，用于空间域到频域的转换
        weight_x = (torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(1, -1) + 0.5) / N
        weight_n = torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(-1, 1)
        weight = torch.cos(weight_n * weight_x * torch.pi) * math.sqrt(2 / N)
        weight[0, :] = weight[0, :] / math.sqrt(2)  # DC分量归一化
        return weight

    @staticmethod
    def get_decay_map(resolution=(14, 14), device=torch.device("cpu"), dtype=torch.float):
        """
        生成基于热传导方程的指数衰减图

        Args:
            resolution (tuple): (H, W) 空间分辨率
            device: 计算设备
            dtype: 数据类型

        Returns:
            torch.Tensor: [H, W] 衰减权重图

        数学原理: exp(-[(nπ/a)² + (mπ/b)²])
        低频分量(n,m接近0)衰减小，高频分量衰减大
        """
        resh, resw = resolution
        # 生成频率坐标
        weight_n = torch.linspace(0, torch.pi, resh + 1, device=device, dtype=dtype)[:resh].view(-1, 1)
        weight_m = torch.linspace(0, torch.pi, resw + 1, device=device, dtype=dtype)[:resw].view(1, -1)
        # 计算频率的平方和并应用指数衰减
        weight = torch.pow(weight_n, 2) + torch.pow(weight_m, 2)
        weight = torch.exp(-weight)
        return weight

    def forward(self, x: torch.Tensor, freq_embed=None):
        """
        Heat2D前向传播，实现基于热传导方程的特征变换

        Args:
            x: [B, H*W, C] 输入序列格式张量 (B=批次大小, H*W=patch数量, C=特征维度)
            freq_embed: [H, W, C] 可选的频率嵌入，用于自适应衰减控制

        Returns:
            [B, H*W, C] 输出序列格式张量，维度与输入相同

        处理流程:
        1. 序列格式 -> 空间格式
        2. 特征变换和门控准备
        3. DCT变换到频域
        4. 应用热传导衰减
        5. IDCT变换回空间域
        6. 门控和输出投影
        7. 空间格式 -> 序列格式
        """
        B, HW, C = x.shape
        H = W = int(math.sqrt(HW))  # 假设patch grid为正方形
        assert H * W == HW, f"序列长度 {HW} 必须是完全平方数"

        # 步骤1: 重塑为空间格式以便进行热传导处理
        x = x.view(B, H, W, C)  # [B, H, W, C]

        # 步骤2: 应用归一化和线性变换
        x = self.norm(x)  # 层归一化
        x = self.linear(x)  # [B, H, W, 2*hidden_dim] 特征扩展
        x, z = x.chunk(chunks=2, dim=-1)  # 分离为主分支x和门控分支z，各为[B, H, W, hidden_dim]

        # 步骤3: 获取或缓存DCT变换矩阵（性能优化）
        if ((H, W) == getattr(self, "__RES__", (0, 0))) and (getattr(self, "__WEIGHT_COSN__", None) is not None):
            # 使用缓存的变换矩阵
            weight_cosn = getattr(self, "__WEIGHT_COSN__", None)
            weight_cosm = getattr(self, "__WEIGHT_COSM__", None)
            weight_exp = getattr(self, "__WEIGHT_EXP__", None)
        else:
            # 重新计算并缓存变换矩阵
            weight_cosn = self.get_cos_map(H, device=x.device).detach_()  # [H, H] DCT矩阵
            weight_cosm = self.get_cos_map(W, device=x.device).detach_()  # [W, W] DCT矩阵
            weight_exp = self.get_decay_map((H, W), device=x.device).detach_()  # [H, W] 衰减图
            setattr(self, "__RES__", (H, W))
            setattr(self, "__WEIGHT_COSN__", weight_cosn)
            setattr(self, "__WEIGHT_COSM__", weight_cosm)
            setattr(self, "__WEIGHT_EXP__", weight_exp)

        N, M = weight_cosn.shape[0], weight_cosm.shape[0]

        # 步骤4: 前向DCT变换 - 空间域到频域
        # 沿H维度进行DCT: [B, H, W*hidden_dim] -> [B, N, W*hidden_dim]
        x = F.conv1d(x.contiguous().view(B, H, -1), weight_cosn.contiguous().view(N, H, 1))
        # 沿W维度进行DCT: [B*N, W, hidden_dim] -> [B*N, M, hidden_dim] -> [B, N, M, hidden_dim]
        x = F.conv1d(x.contiguous().view(-1, W, self.hidden_dim), weight_cosm.contiguous().view(M, W, 1)).contiguous().view(B, N, M, -1)

        # 步骤5: 在频域应用热传导衰减
        if self.infer_mode:
            # 推理模式：使用预计算的自适应衰减权重
            x = torch.einsum("bnmc,nmc->bnmc", x, self.k_exp)
        else:
            # 训练模式：动态计算自适应衰减权重
            if freq_embed is not None:
                # 使用频率嵌入生成自适应衰减权重: [H, W] -> [H, W, hidden_dim]
                weight_exp = torch.pow(weight_exp[:, :, None], self.to_k(freq_embed))
            # 应用衰减: [B, N, M, hidden_dim] * [N, M, hidden_dim] -> [B, N, M, hidden_dim]
            x = torch.einsum("bnmc,nmc->bnmc", x, weight_exp)

        # 步骤6: 逆DCT变换 - 频域到空间域
        # 沿N维度进行IDCT: [B, N, M*hidden_dim] -> [B, H, M*hidden_dim]
        x = F.conv1d(x.contiguous().view(B, N, -1), weight_cosn.t().contiguous().view(H, N, 1))
        # 沿M维度进行IDCT: [B*H, M, hidden_dim] -> [B*H, W, hidden_dim] -> [B, H, W, hidden_dim]
        x = F.conv1d(x.contiguous().view(-1, M, self.hidden_dim), weight_cosm.t().contiguous().view(W, M, 1)).contiguous().view(B, H, W, -1)

        # 步骤7: 应用输出归一化和门控机制
        x = self.out_norm(x)  # 层归一化
        x = x * F.silu(z)  # SiLU门控：主分支与门控分支相乘
        x = self.out_linear(x)  # 输出投影: [B, H, W, hidden_dim] -> [B, H, W, C]

        # 步骤8: 重塑回序列格式
        x = x.view(B, HW, C)  # [B, H*W, C]

        return x


class HeatViTBlock(nn.Module):
    """
    HeatViT变换器块，使用Heat2D机制替代传统的自注意力

    输入格式: [B, H*W, C] (序列格式)
    输出格式: [B, H*W, C] (保持相同格式)

    架构: Heat2D + MLP，类似于标准Transformer的 Attention + MLP 结构
    """

    def __init__(
        self,
        dim: int = 768,
        resolution: int = 14,
        mlp_ratio: float = 4.0,
        drop_path: float = 0.0,
        act_layer: nn.Module = nn.GELU,
        norm_layer: nn.Module = nn.LayerNorm,
        infer_mode: bool = False,
        **kwargs,
    ):
        """
        初始化HeatViT块

        Args:
            dim (int): 特征维度
            resolution (int): 空间分辨率（patch grid大小）
            mlp_ratio (float): MLP扩展比例
            drop_path (float): 随机深度丢弃率
            act_layer: MLP激活函数
            norm_layer: 归一化层类型
            infer_mode (bool): 推理模式
        """
        super().__init__()
        self.dim = dim
        self.resolution = resolution
        self.infer_mode = infer_mode

        # Heat2D模块替代自注意力
        self.heat2d = Heat2D_ViT(
            dim=dim,
            resolution=resolution,
            infer_mode=infer_mode
        )

        # MLP前馈网络
        self.norm2 = norm_layer(dim)  # MLP前的归一化
        mlp_hidden_dim = int(dim * mlp_ratio)  # MLP隐藏层维度
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),  # 扩展: [B, H*W, C] -> [B, H*W, mlp_hidden_dim]
            act_layer(),  # 激活函数
            nn.Linear(mlp_hidden_dim, dim),  # 压缩: [B, H*W, mlp_hidden_dim] -> [B, H*W, C]
        )

        # 随机深度（Stochastic Depth）用于正则化
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x: torch.Tensor, freq_embed=None):
        """
        HeatViT块前向传播

        Args:
            x: [B, H*W, C] 输入序列张量
            freq_embed: [H, W, C] 频率嵌入（可选）

        Returns:
            [B, H*W, C] 输出序列张量

        计算流程:
        1. Heat2D处理（替代自注意力） + 残差连接
        2. MLP处理 + 残差连接
        """
        # Heat2D块（替代自注意力）+ 残差连接
        x = x + self.drop_path(self.heat2d(x, freq_embed))

        # MLP块 + 残差连接
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


class HeatViT(nn.Module):
    """
    基于Heat2D机制的Vision Transformer，用热传导方程替代自注意力

    输入格式: [B, C, H, W] (标准图像格式)
    输出格式:
        - 分类模式: [B, num_classes]
        - 特征提取模式: [B, H*W, C] (当num_classes=0时)

    架构特点:
    1. 使用卷积进行patch embedding
    2. 每层都有独立的可学习频率嵌入
    3. 多个HeatViT块堆叠
    4. 可选的分类头
    """

    def __init__(
        self,
        img_size: int = 224,
        patch_size: int = 16,
        in_chans: int = 3,
        embed_dim: int = 768,
        depth: int = 12,
        mlp_ratio: float = 4.0,
        drop_path_rate: float = 0.1,
        num_classes: int = 1000,
        infer_mode: bool = False,
        **kwargs
    ):
        """
        初始化HeatViT模型

        Args:
            img_size (int): 输入图像尺寸，默认224
            patch_size (int): patch大小，默认16 (224/16=14个patch)
            in_chans (int): 输入通道数，默认3 (RGB)
            embed_dim (int): 嵌入维度，默认768
            depth (int): 变换器层数，默认12
            mlp_ratio (float): MLP扩展比例，默认4.0
            drop_path_rate (float): 随机深度丢弃率，默认0.1
            num_classes (int): 分类类别数，0表示特征提取模式
            infer_mode (bool): 推理模式，预计算权重以加速
        """
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.depth = depth
        self.num_classes = num_classes
        self.infer_mode = infer_mode

        # 计算patch相关参数
        self.num_patches = (img_size // patch_size) ** 2  # 总patch数量
        self.patch_resolution = img_size // patch_size  # patch grid分辨率

        # Patch嵌入层：将图像分割为patches并投影到嵌入空间
        self.patch_embed = nn.Conv2d(
            in_chans, embed_dim,
            kernel_size=patch_size,  # [B, 3, 224, 224] -> [B, 768, 14, 14]
            stride=patch_size
        )

        # 位置嵌入（每层独立的可学习频率嵌入）
        # 替代传统的位置编码，用于Heat2D的自适应衰减控制
        self.freq_embeds = nn.ParameterList([
            nn.Parameter(torch.zeros(self.patch_resolution, self.patch_resolution, embed_dim))
            for _ in range(depth)  # 每层一个频率嵌入: [14, 14, 768]
        ])

        # 变换器块堆叠
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)]  # 随机深度衰减率
        self.blocks = nn.ModuleList([
            HeatViTBlock(
                dim=embed_dim,
                resolution=self.patch_resolution,
                mlp_ratio=mlp_ratio,
                drop_path=dpr[i],  # 每层不同的丢弃率
                infer_mode=infer_mode,
            )
            for i in range(depth)
        ])

        # 分类头
        self.norm = nn.LayerNorm(embed_dim)  # 最终归一化
        if num_classes > 0:
            # 分类模式：全连接层
            self.head = nn.Linear(embed_dim, num_classes)  # [B, 768] -> [B, num_classes]
        else:
            # 特征提取模式：恒等映射
            self.head = nn.Identity()

        # 权重初始化
        self.apply(self._init_weights)
        for freq_embed in self.freq_embeds:
            trunc_normal_(freq_embed, std=.02)  # 频率嵌入使用截断正态分布初始化

    def _init_weights(self, m):
        """
        权重初始化函数

        Args:
            m: 网络模块
        """
        if isinstance(m, nn.Linear):
            # 线性层：权重使用截断正态分布，偏置置零
            trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            # 层归一化：偏置置零，权重置一
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            # 卷积层：权重使用截断正态分布，偏置置零
            trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)

    def infer_init(self):
        """
        推理模式初始化，预计算所有层的衰减权重以加速推理
        调用此方法后模型进入推理模式，不再需要频率嵌入
        """
        for i, block in enumerate(self.blocks):
            block.heat2d.infer_init_heat2d(self.freq_embeds[i])
        del self.freq_embeds  # 删除频率嵌入以节省内存

    def forward_features(self, x):
        """
        特征提取前向传播（不包含分类头）

        Args:
            x: [B, C, H, W] 输入图像张量

        Returns:
            [B, H*W, C] 特征序列张量

        处理流程:
        1. Patch嵌入：图像 -> patch序列
        2. 通过所有HeatViT块
        3. 返回特征序列
        """
        # Patch嵌入：[B, C, H, W] -> [B, embed_dim, H//patch_size, W//patch_size]
        x = self.patch_embed(x)

        # 展平为序列：[B, embed_dim, H', W'] -> [B, H'*W', embed_dim]
        x = x.flatten(2).transpose(1, 2)  # [B, num_patches, embed_dim]

        # 通过变换器块
        if self.infer_mode:
            # 推理模式：使用预计算的权重
            for block in self.blocks:
                x = block(x)
        else:
            # 训练模式：使用动态频率嵌入
            for i, block in enumerate(self.blocks):
                x = block(x, self.freq_embeds[i])

        return x

    def forward(self, x):
        """
        完整的前向传播

        Args:
            x: [B, C, H, W] 输入图像张量

        Returns:
            - 分类模式 (num_classes > 0): [B, num_classes] 分类logits
            - 特征提取模式 (num_classes = 0): [B, H*W, C] 特征序列

        处理流程:
        1. 特征提取
        2. 可选的分类头处理
        """
        x = self.forward_features(x)  # [B, H*W, C] 提取特征

        if self.num_classes > 0:
            # 分类模式：全局平均池化 + 分类
            x = self.norm(x)  # 最终归一化
            x = x.mean(dim=1)  # 全局平均池化: [B, H*W, C] -> [B, C]
            x = self.head(x)   # 分类头: [B, C] -> [B, num_classes]

        return x


# ================================
# 模型工厂函数 - 不同规模的HeatViT模型
# ================================

def heat_vit_tiny(img_size=224, **kwargs):
    """
    创建Tiny规模的HeatViT模型

    参数配置:
    - embed_dim: 192 (较小的嵌入维度)
    - depth: 12 (12层)
    - 参数量: ~5.7M

    适用场景: 资源受限环境，快速原型验证
    """
    return HeatViT(
        img_size=img_size, patch_size=16, embed_dim=192, depth=12,
        mlp_ratio=4, **kwargs
    )

def heat_vit_small(img_size=224, **kwargs):
    """
    创建Small规模的HeatViT模型

    参数配置:
    - embed_dim: 384 (中等嵌入维度)
    - depth: 12 (12层)
    - 参数量: ~22M

    适用场景: 平衡性能和效率的应用
    """
    return HeatViT(
        img_size=img_size, patch_size=16, embed_dim=384, depth=12,
        mlp_ratio=4, **kwargs
    )

def heat_vit_base(img_size=224, **kwargs):
    """
    创建Base规模的HeatViT模型（标准配置）

    参数配置:
    - embed_dim: 768 (标准嵌入维度)
    - depth: 12 (12层)
    - 参数量: ~86M

    适用场景: 大多数视觉任务的标准选择
    """
    return HeatViT(
        img_size=img_size, patch_size=16, embed_dim=768, depth=12,
        mlp_ratio=4, **kwargs
    )

def heat_vit_large(img_size=224, **kwargs):
    """
    创建Large规模的HeatViT模型

    参数配置:
    - embed_dim: 1024 (大嵌入维度)
    - depth: 24 (24层，更深的网络)
    - 参数量: ~307M

    适用场景: 需要最高性能的复杂视觉任务
    """
    return HeatViT(
        img_size=img_size, patch_size=16, embed_dim=1024, depth=24,
        mlp_ratio=4, **kwargs
    )


if __name__ == "__main__":
    """
    测试代码：验证模型的输入输出形状
    """
    print("=" * 50)
    print("HeatViT模型测试")
    print("=" * 50)

    # 测试特征提取模式
    print("\n1. 特征提取模式测试:")
    model = heat_vit_base(num_classes=0)  # 特征提取模式
    x = torch.randn(2, 3, 224, 224)  # 批次大小=2, RGB图像, 224x224

    print(f"输入形状: {x.shape}")  # [2, 3, 224, 224]
    features = model(x)
    print(f"输出形状: {features.shape}")  # 应该是 [2, 196, 768] (224/16=14, 14*14=196)

    # 测试分类模式
    print("\n2. 分类模式测试:")
    model_cls = heat_vit_base(num_classes=1000)  # ImageNet分类
    logits = model_cls(x)
    print(f"分类输出形状: {logits.shape}")  # 应该是 [2, 1000]

    print("\n测试完成！")
