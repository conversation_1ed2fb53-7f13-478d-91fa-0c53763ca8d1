import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial
from typing import Optional, Callable
from timm.models.layers import DropPath, trunc_normal_


class Heat2D_ViT(nn.Module):
    """
    Heat2D module adapted for ViT-like architecture
    Input/Output: [B, H*W, C] (sequence format)

    Based on heat equation solution:
    du/dt - k(d2u/dx2 + d2u/dy2) = 0
    Solution: u(x,y,t) = sum(A_nm * cos(nπx/a) * cos(mπy/b) * exp(-[(nπ/a)² + (mπ/b)²]kt))
    """

    def __init__(self, dim=768, hidden_dim=None, resolution=14, infer_mode=False, **kwargs):
        super().__init__()
        hidden_dim = hidden_dim or dim
        self.dim = dim
        self.hidden_dim = hidden_dim
        self.resolution = resolution
        self.infer_mode = infer_mode

        # Linear projections
        self.norm = nn.LayerNorm(dim)
        self.linear = nn.Linear(dim, 2 * hidden_dim, bias=True)
        self.out_norm = nn.LayerNorm(hidden_dim)
        self.out_linear = nn.Linear(hidden_dim, dim, bias=True)

        # Frequency embedding transformation
        self.to_k = nn.Sequential(
            nn.Linear(dim, hidden_dim, bias=True),
            nn.ReLU(),
        )

    def infer_init_heat2d(self, freq_embed):
        """Initialize for inference mode"""
        weight_exp = self.get_decay_map((self.resolution, self.resolution), device=freq_embed.device)
        self.k_exp = nn.Parameter(
            torch.pow(weight_exp[:, :, None], self.to_k(freq_embed)),
            requires_grad=False
        )
        del self.to_k

    @staticmethod
    def get_cos_map(N=14, device=torch.device("cpu"), dtype=torch.float):
        """Generate DCT/IDCT transformation matrix"""
        # cos((x + 0.5) / N * n * π) - DCT/IDCT form
        weight_x = (torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(1, -1) + 0.5) / N
        weight_n = torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(-1, 1)
        weight = torch.cos(weight_n * weight_x * torch.pi) * math.sqrt(2 / N)
        weight[0, :] = weight[0, :] / math.sqrt(2)
        return weight

    @staticmethod
    def get_decay_map(resolution=(14, 14), device=torch.device("cpu"), dtype=torch.float):
        """Generate exponential decay map based on heat equation"""
        # exp(-[(nπ/a)² + (mπ/b)²])
        resh, resw = resolution
        weight_n = torch.linspace(0, torch.pi, resh + 1, device=device, dtype=dtype)[:resh].view(-1, 1)
        weight_m = torch.linspace(0, torch.pi, resw + 1, device=device, dtype=dtype)[:resw].view(1, -1)
        weight = torch.pow(weight_n, 2) + torch.pow(weight_m, 2)
        weight = torch.exp(-weight)
        return weight

    def forward(self, x: torch.Tensor, freq_embed=None):
        """
        Args:
            x: [B, H*W, C] - sequence format
            freq_embed: [H, W, C] - frequency embedding
        Returns:
            [B, H*W, C] - sequence format
        """
        B, HW, C = x.shape
        H = W = int(math.sqrt(HW))  # Assume square patches
        assert H * W == HW, f"Sequence length {HW} must be a perfect square"

        # Reshape to spatial format for heat equation processing
        x = x.view(B, H, W, C)  # [B, H, W, C]

        # Apply normalization and linear transformation
        x = self.norm(x)
        x = self.linear(x)  # [B, H, W, 2*hidden_dim]
        x, z = x.chunk(chunks=2, dim=-1)  # [B, H, W, hidden_dim] each

        # Get or cache transformation matrices
        if ((H, W) == getattr(self, "__RES__", (0, 0))) and (getattr(self, "__WEIGHT_COSN__", None) is not None):
            weight_cosn = getattr(self, "__WEIGHT_COSN__", None)
            weight_cosm = getattr(self, "__WEIGHT_COSM__", None)
            weight_exp = getattr(self, "__WEIGHT_EXP__", None)
        else:
            weight_cosn = self.get_cos_map(H, device=x.device).detach_()
            weight_cosm = self.get_cos_map(W, device=x.device).detach_()
            weight_exp = self.get_decay_map((H, W), device=x.device).detach_()
            setattr(self, "__RES__", (H, W))
            setattr(self, "__WEIGHT_COSN__", weight_cosn)
            setattr(self, "__WEIGHT_COSM__", weight_cosm)
            setattr(self, "__WEIGHT_EXP__", weight_exp)

        N, M = weight_cosn.shape[0], weight_cosm.shape[0]

        # Forward DCT: spatial -> frequency domain
        x = F.conv1d(x.contiguous().view(B, H, -1), weight_cosn.contiguous().view(N, H, 1))
        x = F.conv1d(x.contiguous().view(-1, W, self.hidden_dim), weight_cosm.contiguous().view(M, W, 1)).contiguous().view(B, N, M, -1)

        # Apply heat equation decay in frequency domain
        if self.infer_mode:
            x = torch.einsum("bnmc,nmc->bnmc", x, self.k_exp)
        else:
            if freq_embed is not None:
                weight_exp = torch.pow(weight_exp[:, :, None], self.to_k(freq_embed))
            x = torch.einsum("bnmc,nmc->bnmc", x, weight_exp)

        # Inverse DCT: frequency -> spatial domain
        x = F.conv1d(x.contiguous().view(B, N, -1), weight_cosn.t().contiguous().view(H, N, 1))
        x = F.conv1d(x.contiguous().view(-1, M, self.hidden_dim), weight_cosm.t().contiguous().view(W, M, 1)).contiguous().view(B, H, W, -1)

        # Apply output normalization and gating
        x = self.out_norm(x)
        x = x * F.silu(z)  # SiLU gating
        x = self.out_linear(x)

        # Reshape back to sequence format
        x = x.view(B, HW, C)  # [B, H*W, C]

        return x


class HeatViTBlock(nn.Module):
    """
    ViT-like transformer block using Heat2D instead of self-attention
    """

    def __init__(
        self,
        dim: int = 768,
        resolution: int = 14,
        mlp_ratio: float = 4.0,
        drop_path: float = 0.0,
        act_layer: nn.Module = nn.GELU,
        norm_layer: nn.Module = nn.LayerNorm,
        infer_mode: bool = False,
        **kwargs,
    ):
        super().__init__()
        self.dim = dim
        self.resolution = resolution
        self.infer_mode = infer_mode

        # Heat2D replaces self-attention
        self.heat2d = Heat2D_ViT(
            dim=dim,
            resolution=resolution,
            infer_mode=infer_mode
        )

        # MLP block
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            act_layer(),
            nn.Linear(mlp_hidden_dim, dim),
        )

        # Drop path for stochastic depth
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x: torch.Tensor, freq_embed=None):
        """
        Args:
            x: [B, H*W, C]
            freq_embed: [H, W, C] - frequency embedding
        Returns:
            [B, H*W, C]
        """
        # Heat2D block (replaces self-attention)
        x = x + self.drop_path(self.heat2d(x, freq_embed))

        # MLP block
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


class HeatViT(nn.Module):
    """
    Vision Transformer using Heat2D mechanism instead of self-attention

    Args:
        img_size: Input image size
        patch_size: Patch size for tokenization
        in_chans: Number of input channels
        embed_dim: Embedding dimension
        depth: Number of transformer blocks
        mlp_ratio: MLP expansion ratio
        drop_path_rate: Stochastic depth rate
        num_classes: Number of output classes (0 for feature extraction)
    """

    def __init__(
        self,
        img_size: int = 224,
        patch_size: int = 16,
        in_chans: int = 3,
        embed_dim: int = 768,
        depth: int = 12,
        mlp_ratio: float = 4.0,
        drop_path_rate: float = 0.1,
        num_classes: int = 1000,
        infer_mode: bool = False,
        **kwargs
    ):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.depth = depth
        self.num_classes = num_classes
        self.infer_mode = infer_mode

        # Calculate patch resolution
        self.num_patches = (img_size // patch_size) ** 2
        self.patch_resolution = img_size // patch_size

        # Patch embedding
        self.patch_embed = nn.Conv2d(
            in_chans, embed_dim,
            kernel_size=patch_size,
            stride=patch_size
        )

        # Positional embedding (learnable frequency embedding for each layer)
        self.freq_embeds = nn.ParameterList([
            nn.Parameter(torch.zeros(self.patch_resolution, self.patch_resolution, embed_dim))
            for _ in range(depth)
        ])

        # Transformer blocks
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)]
        self.blocks = nn.ModuleList([
            HeatViTBlock(
                dim=embed_dim,
                resolution=self.patch_resolution,
                mlp_ratio=mlp_ratio,
                drop_path=dpr[i],
                infer_mode=infer_mode,
            )
            for i in range(depth)
        ])

        # Classification head
        self.norm = nn.LayerNorm(embed_dim)
        if num_classes > 0:
            self.head = nn.Linear(embed_dim, num_classes)
        else:
            self.head = nn.Identity()

        # Initialize weights
        self.apply(self._init_weights)
        for freq_embed in self.freq_embeds:
            trunc_normal_(freq_embed, std=.02)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)

    def infer_init(self):
        """Initialize for inference mode"""
        for i, block in enumerate(self.blocks):
            block.heat2d.infer_init_heat2d(self.freq_embeds[i])
        del self.freq_embeds

    def forward_features(self, x):
        """
        Args:
            x: [B, C, H, W]
        Returns:
            [B, H*W, C]
        """
        B = x.shape[0]

        # Patch embedding: [B, C, H, W] -> [B, embed_dim, H//patch_size, W//patch_size]
        x = self.patch_embed(x)

        # Flatten to sequence: [B, embed_dim, H', W'] -> [B, H'*W', embed_dim]
        x = x.flatten(2).transpose(1, 2)  # [B, num_patches, embed_dim]

        # Apply transformer blocks
        if self.infer_mode:
            for block in self.blocks:
                x = block(x)
        else:
            for i, block in enumerate(self.blocks):
                x = block(x, self.freq_embeds[i])

        return x

    def forward(self, x):
        """
        Args:
            x: [B, C, H, W]
        Returns:
            [B, num_classes] if num_classes > 0, else [B, H*W, C]
        """
        x = self.forward_features(x)  # [B, H*W, C]

        if self.num_classes > 0:
            # Global average pooling + classification
            x = self.norm(x)
            x = x.mean(dim=1)  # [B, C]
            x = self.head(x)   # [B, num_classes]

        return x


# Factory functions for different model sizes
def heat_vit_tiny(img_size=224, **kwargs):
    return HeatViT(
        img_size=img_size, patch_size=16, embed_dim=192, depth=12,
        mlp_ratio=4, **kwargs
    )

def heat_vit_small(img_size=224, **kwargs):
    return HeatViT(
        img_size=img_size, patch_size=16, embed_dim=384, depth=12,
        mlp_ratio=4, **kwargs
    )

def heat_vit_base(img_size=224, **kwargs):
    return HeatViT(
        img_size=img_size, patch_size=16, embed_dim=768, depth=12,
        mlp_ratio=4, **kwargs
    )

def heat_vit_large(img_size=224, **kwargs):
    return HeatViT(
        img_size=img_size, patch_size=16, embed_dim=1024, depth=24,
        mlp_ratio=4, **kwargs
    )


if __name__ == "__main__":
    # Test the model
    model = heat_vit_base(num_classes=0)  # Feature extraction mode
    x = torch.randn(2, 3, 224, 224)

    print(f"Input shape: {x.shape}")
    features = model(x)
    print(f"Output shape: {features.shape}")  # Should be [2, 196, 768] for 224x224 input

    # Test classification mode
    model_cls = heat_vit_base(num_classes=1000)
    logits = model_cls(x)
    print(f"Classification output shape: {logits.shape}")  # Should be [2, 1000]
