"""
vHeat: 基于热传导方程的视觉变换器
使用热传导方程替代传统的自注意力机制，实现高效的视觉特征学习

主要特点:
1. 使用DCT/IDCT进行频域变换
2. 在频域应用热传导衰减
3. 支持多尺度特征提取
4. 兼容标准CNN架构的下采样策略
"""

import time
import math
from functools import partial
from typing import Optional, Callable

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint as checkpoint
from einops import rearrange, repeat
from timm.models.layers import DropPath, to_2tuple, trunc_normal_

DropPath.__repr__ = lambda self: f"timm.DropPath({self.drop_prob})"


class LayerNorm2d(nn.LayerNorm):
    """
    2D层归一化，适用于channels_first格式的特征图

    输入格式: [B, C, H, W]
    输出格式: [B, C, H, W]

    处理流程: channels_first -> channels_last -> LayerNorm -> channels_first
    """
    def forward(self, x: torch.Tensor):
        # [B, C, H, W] -> [B, H, W, C]
        x = x.permute(0, 2, 3, 1).contiguous()
        # 在最后一个维度上进行层归一化
        x = F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        # [B, H, W, C] -> [B, C, H, W]
        x = x.permute(0, 3, 1, 2).contiguous()
        return x


class to_channels_first(nn.Module):
    """
    格式转换模块：channels_last -> channels_first
    [B, H, W, C] -> [B, C, H, W]
    """
    def __init__(self):
        super().__init__()

    def forward(self, x):
        return x.permute(0, 3, 1, 2).contiguous()


class to_channels_last(nn.Module):
    """
    格式转换模块：channels_first -> channels_last
    [B, C, H, W] -> [B, H, W, C]
    """
    def __init__(self):
        super().__init__()

    def forward(self, x):
        return x.permute(0, 2, 3, 1).contiguous()


def build_norm_layer(dim,
                     norm_layer,
                     in_format='channels_last',
                     out_format='channels_last',
                     eps=1e-6):
    """
    构建归一化层，支持不同的输入输出格式

    Args:
        dim (int): 特征维度
        norm_layer (str): 归一化类型 ('BN' 或 'LN')
        in_format (str): 输入格式 ('channels_first' 或 'channels_last')
        out_format (str): 输出格式 ('channels_first' 或 'channels_last')
        eps (float): LayerNorm的epsilon参数

    Returns:
        nn.Sequential: 包含格式转换和归一化的序列模块
    """
    layers = []
    if norm_layer == 'BN':
        # BatchNorm需要channels_first格式
        if in_format == 'channels_last':
            layers.append(to_channels_first())
        layers.append(nn.BatchNorm2d(dim))
        if out_format == 'channels_last':
            layers.append(to_channels_last())
    elif norm_layer == 'LN':
        # LayerNorm需要channels_last格式
        if in_format == 'channels_first':
            layers.append(to_channels_last())
        layers.append(nn.LayerNorm(dim, eps=eps))
        if out_format == 'channels_first':
            layers.append(to_channels_first())
    else:
        raise NotImplementedError(
            f'build_norm_layer does not support {norm_layer}')
    return nn.Sequential(*layers)


def build_act_layer(act_layer):
    """
    构建激活函数层

    Args:
        act_layer (str): 激活函数类型

    Returns:
        nn.Module: 激活函数模块
    """
    if act_layer == 'ReLU':
        return nn.ReLU(inplace=True)
    elif act_layer == 'SiLU':
        return nn.SiLU(inplace=True)
    elif act_layer == 'GELU':
        return nn.GELU()

    raise NotImplementedError(f'build_act_layer does not support {act_layer}')


class StemLayer(nn.Module):
    """
    Stem层：图像预处理和初始特征提取

    输入格式: [B, in_chans, H, W]
    输出格式: [B, out_chans, H//4, W//4]

    架构: Conv3x3(stride=2) -> Norm -> Act -> Conv3x3(stride=2) -> Norm
    总体下采样倍数: 4x

    Args:
        in_chans (int): 输入通道数，默认3 (RGB)
        out_chans (int): 输出通道数，默认96
        act_layer (str): 激活函数类型，默认'GELU'
        norm_layer (str): 归一化类型，默认'BN'
    """

    def __init__(self,
                 in_chans=3,
                 out_chans=96,
                 act_layer='GELU',
                 norm_layer='BN'):
        super().__init__()
        # 第一个卷积：降采样2x，通道数扩展到out_chans//2
        self.conv1 = nn.Conv2d(in_chans,
                               out_chans // 2,
                               kernel_size=3,
                               stride=2,
                               padding=1)
        self.norm1 = build_norm_layer(out_chans // 2, norm_layer,
                                      'channels_first', 'channels_first')
        self.act = build_act_layer(act_layer)
        # 第二个卷积：再次降采样2x，通道数扩展到out_chans
        self.conv2 = nn.Conv2d(out_chans // 2,
                               out_chans,
                               kernel_size=3,
                               stride=2,
                               padding=1)
        self.norm2 = build_norm_layer(out_chans, norm_layer, 'channels_first',
                                      'channels_first')

    def forward(self, x):
        """
        前向传播

        Args:
            x: [B, in_chans, H, W] 输入图像

        Returns:
            [B, out_chans, H//4, W//4] 下采样后的特征图
        """
        x = self.conv1(x)      # [B, in_chans, H, W] -> [B, out_chans//2, H//2, W//2]
        x = self.norm1(x)
        x = self.act(x)
        x = self.conv2(x)      # [B, out_chans//2, H//2, W//2] -> [B, out_chans, H//4, W//4]
        x = self.norm2(x)
        return x


class Mlp(nn.Module):
    """
    多层感知机（MLP）模块，支持channels_first和channels_last格式

    输入格式:
        - channels_first: [B, C, H, W]
        - channels_last: [B, H, W, C] 或 [B, N, C]
    输出格式: 与输入格式相同

    架构: Linear -> Act -> Dropout -> Linear -> Dropout
    """
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.,channels_first=False):
        """
        初始化MLP模块

        Args:
            in_features (int): 输入特征维度
            hidden_features (int): 隐藏层特征维度，默认等于in_features
            out_features (int): 输出特征维度，默认等于in_features
            act_layer: 激活函数类，默认GELU
            drop (float): Dropout概率
            channels_first (bool): 是否使用channels_first格式（使用1x1卷积代替线性层）
        """
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features

        # 根据格式选择线性层类型
        Linear = partial(nn.Conv2d, kernel_size=1, padding=0) if channels_first else nn.Linear
        self.fc1 = Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入张量，格式取决于初始化时的channels_first参数

        Returns:
            输出张量，格式与输入相同
        """
        x = self.fc1(x)        # 第一个线性变换
        x = self.act(x)        # 激活函数
        x = self.drop(x)       # Dropout
        x = self.fc2(x)        # 第二个线性变换
        x = self.drop(x)       # Dropout
        return x


class Heat2D(nn.Module):
    """
    Heat2D模块：基于热传导方程的特征变换

    输入格式: [B, C, H, W]
    输出格式: [B, C, H, W]

    数学原理:
    热传导方程: du/dt - k(d²u/dx² + d²u/dy²) = 0
    边界条件: du/dx|_{x=0,a} = 0, du/dy|_{y=0,b} = 0

    解析解: u(x,y,t) = Σ Σ A_{n,m} * cos(nπx/a) * cos(mπy/b) * exp(-[(nπ/a)² + (mπ/b)²]kt)

    实现步骤:
    1. φ(x,y) = linear(dwconv(input(x,y)))  # 特征预处理
    2. A(n,m) = DCT2D(φ(x,y))              # 空间域 -> 频域
    3. u(x,y,t) = IDCT2D(A(n,m) * exp(-[(nπ/a)² + (mπ/b)²]^kt))  # 频域衰减 -> 空间域

    核心思想: 在频域应用热传导衰减，低频保留，高频衰减
    """
    def __init__(self, infer_mode=False, res=14, dim=96, hidden_dim=96, **kwargs):
        """
        初始化Heat2D模块

        Args:
            infer_mode (bool): 推理模式，预计算衰减权重
            res (int): 空间分辨率，默认14
            dim (int): 输入特征维度，默认96
            hidden_dim (int): 隐藏特征维度，默认96
        """
        super().__init__()
        self.res = res
        # 深度可分离卷积，用于特征预处理
        self.dwconv = nn.Conv2d(dim, hidden_dim, kernel_size=3, padding=1, groups=hidden_dim)
        self.hidden_dim = hidden_dim
        # 线性变换，生成主分支和门控分支
        self.linear = nn.Linear(hidden_dim, 2 * hidden_dim, bias=True)
        # 输出归一化和投影
        self.out_norm = nn.LayerNorm(hidden_dim)
        self.out_linear = nn.Linear(hidden_dim, hidden_dim, bias=True)
        self.infer_mode = infer_mode
        # 频率嵌入到衰减系数的变换网络
        self.to_k = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim, bias=True),
            nn.ReLU(),
        )

    def infer_init_heat2d(self, freq):
        """
        推理模式初始化，预计算衰减权重

        Args:
            freq: [H, W, C] 频率嵌入张量
        """
        weight_exp = self.get_decay_map((self.res, self.res), device=freq.device)
        # 预计算自适应衰减权重: [H, W, hidden_dim]
        self.k_exp = nn.Parameter(torch.pow(weight_exp[:, :, None], self.to_k(freq)), requires_grad=False)
        del self.to_k  # 删除不再需要的网络

    @staticmethod
    def get_cos_map(N=224, device=torch.device("cpu"), dtype=torch.float):
        """
        生成DCT/IDCT变换矩阵

        Args:
            N (int): 变换大小
            device: 计算设备
            dtype: 数据类型

        Returns:
            torch.Tensor: [N, N] DCT变换矩阵

        数学原理:
        DCT基函数: cos((x + 0.5) / N * n * π)
        DCT: F(n) = Σ (√(2/N) if n>0 else √(1/N)) * cos((x+0.5)/N * n * π) * f(x)
        IDCT: f(x) = Σ (√(2/N) if n>0 else √(1/N)) * cos((x+0.5)/N * n * π) * F(n)
        """
        # 生成空间坐标和频率坐标
        weight_x = (torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(1, -1) + 0.5) / N
        weight_n = torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(-1, 1)
        # 计算DCT变换矩阵
        weight = torch.cos(weight_n * weight_x * torch.pi) * math.sqrt(2 / N)
        weight[0, :] = weight[0, :] / math.sqrt(2)  # DC分量归一化
        return weight

    @staticmethod
    def get_decay_map(resolution=(224, 224), device=torch.device("cpu"), dtype=torch.float):
        """
        生成基于热传导方程的指数衰减图

        Args:
            resolution (tuple): (H, W) 空间分辨率
            device: 计算设备
            dtype: 数据类型

        Returns:
            torch.Tensor: [H, W] 衰减权重图

        数学原理: exp(-[(nπ/a)² + (mπ/b)²])
        低频分量(n,m接近0)衰减小，高频分量衰减大
        """
        resh, resw = resolution
        # 生成频率坐标
        weight_n = torch.linspace(0, torch.pi, resh + 1, device=device, dtype=dtype)[:resh].view(-1, 1)
        weight_m = torch.linspace(0, torch.pi, resw + 1, device=device, dtype=dtype)[:resw].view(1, -1)
        # 计算频率的平方和并应用指数衰减
        weight = torch.pow(weight_n, 2) + torch.pow(weight_m, 2)
        weight = torch.exp(-weight)
        return weight

    def forward(self, x: torch.Tensor, freq_embed=None):
        """
        Heat2D前向传播，实现基于热传导方程的特征变换

        Args:
            x: [B, C, H, W] 输入特征图
            freq_embed: [H, W, C] 可选的频率嵌入，用于自适应衰减控制

        Returns:
            [B, C, H, W] 输出特征图，维度与输入相同

        处理流程:
        1. 深度可分离卷积预处理
        2. 线性变换和门控分支分离
        3. DCT变换到频域
        4. 应用热传导衰减
        5. IDCT变换回空间域
        6. 门控和输出投影
        """
        B, C, H, W = x.shape
        # 步骤1: 深度可分离卷积预处理
        x = self.dwconv(x)  # [B, C, H, W] -> [B, hidden_dim, H, W]

        # 步骤2: 格式转换并进行线性变换
        x = self.linear(x.permute(0, 2, 3, 1).contiguous())  # [B, H, W, 2*hidden_dim]
        x, z = x.chunk(chunks=2, dim=-1)  # 分离主分支x和门控分支z: [B, H, W, hidden_dim] each

        # 步骤3: 获取或缓存DCT变换矩阵（性能优化）
        if ((H, W) == getattr(self, "__RES__", (0, 0))) and (getattr(self, "__WEIGHT_COSN__", None).device == x.device):
            # 使用缓存的变换矩阵
            weight_cosn = getattr(self, "__WEIGHT_COSN__", None)
            weight_cosm = getattr(self, "__WEIGHT_COSM__", None)
            weight_exp = getattr(self, "__WEIGHT_EXP__", None)
            assert weight_cosn is not None
            assert weight_cosm is not None
            assert weight_exp is not None
        else:
            # 重新计算并缓存变换矩阵
            weight_cosn = self.get_cos_map(H, device=x.device).detach_()  # [H, H] DCT矩阵
            weight_cosm = self.get_cos_map(W, device=x.device).detach_()  # [W, W] DCT矩阵
            weight_exp = self.get_decay_map((H, W), device=x.device).detach_()  # [H, W] 衰减图
            setattr(self, "__RES__", (H, W))
            setattr(self, "__WEIGHT_COSN__", weight_cosn)
            setattr(self, "__WEIGHT_COSM__", weight_cosm)
            setattr(self, "__WEIGHT_EXP__", weight_exp)

        N, M = weight_cosn.shape[0], weight_cosm.shape[0]

        # 步骤4: 前向DCT变换 - 空间域到频域
        # 沿H维度进行DCT: [B, H, W*hidden_dim] -> [B, N, W*hidden_dim]
        x = F.conv1d(x.contiguous().view(B, H, -1), weight_cosn.contiguous().view(N, H, 1))
        # 沿W维度进行DCT: [B*N, W, hidden_dim] -> [B*N, M, hidden_dim] -> [B, N, M, hidden_dim]
        x = F.conv1d(x.contiguous().view(-1, W, self.hidden_dim), weight_cosm.contiguous().view(M, W, 1)).contiguous().view(B, N, M, -1)

        # 步骤5: 在频域应用热传导衰减
        if self.infer_mode:
            # 推理模式：使用预计算的自适应衰减权重
            x = torch.einsum("bnmc,nmc->bnmc", x, self.k_exp)
        else:
            # 训练模式：动态计算自适应衰减权重
            weight_exp = torch.pow(weight_exp[:, :, None], self.to_k(freq_embed))  # [H, W, hidden_dim]
            x = torch.einsum("bnmc,nmc -> bnmc", x, weight_exp)  # 应用指数衰减

        # 步骤6: 逆DCT变换 - 频域到空间域
        # 沿N维度进行IDCT: [B, N, M*hidden_dim] -> [B, H, M*hidden_dim]
        x = F.conv1d(x.contiguous().view(B, N, -1), weight_cosn.t().contiguous().view(H, N, 1))
        # 沿M维度进行IDCT: [B*H, M, hidden_dim] -> [B*H, W, hidden_dim] -> [B, H, W, hidden_dim]
        x = F.conv1d(x.contiguous().view(-1, M, self.hidden_dim), weight_cosm.t().contiguous().view(W, M, 1)).contiguous().view(B, H, W, -1)

        # 步骤7: 应用输出归一化和门控机制
        x = self.out_norm(x)  # 层归一化
        x = x * nn.functional.silu(z)  # SiLU门控：主分支与门控分支相乘
        x = self.out_linear(x)  # 输出投影: [B, H, W, hidden_dim] -> [B, H, W, hidden_dim]

        # 步骤8: 转换回channels_first格式
        x = x.permute(0, 3, 1, 2).contiguous()  # [B, H, W, hidden_dim] -> [B, hidden_dim, H, W]

        return x


class HeatBlock(nn.Module):
    """
    HeatBlock：基于Heat2D的变换器块

    输入格式: [B, C, H, W]
    输出格式: [B, C, H, W]

    架构: Heat2D + MLP，类似于标准Transformer的 Attention + FFN 结构
    支持pre-norm和post-norm两种归一化方式
    """
    def __init__(
        self,
        res: int = 14,
        infer_mode = False,
        hidden_dim: int = 0,
        drop_path: float = 0,
        norm_layer: Callable[..., torch.nn.Module] = partial(nn.LayerNorm, eps=1e-6),
        use_checkpoint: bool = False,
        drop: float = 0.0,
        act_layer: nn.Module = nn.GELU,
        mlp_ratio: float = 4.0,
        post_norm = True,
        layer_scale = None,
        **kwargs,
    ):
        """
        初始化HeatBlock

        Args:
            res (int): 空间分辨率
            infer_mode (bool): 推理模式
            hidden_dim (int): 隐藏维度
            drop_path (float): 随机深度丢弃率
            norm_layer: 归一化层类型
            use_checkpoint (bool): 是否使用梯度检查点
            drop (float): MLP中的dropout率
            act_layer: MLP激活函数
            mlp_ratio (float): MLP扩展比例
            post_norm (bool): 是否使用post-norm（False为pre-norm）
            layer_scale: 层缩放初始值
        """
        super().__init__()
        self.use_checkpoint = use_checkpoint
        # Heat2D分支的归一化
        self.norm1 = norm_layer(hidden_dim)
        # Heat2D操作（替代自注意力）
        self.op = Heat2D(res=res, dim=hidden_dim, hidden_dim=hidden_dim, infer_mode=infer_mode)
        # 随机深度
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        # MLP分支
        self.mlp_branch = mlp_ratio > 0
        if self.mlp_branch:
            self.norm2 = norm_layer(hidden_dim)
            mlp_hidden_dim = int(hidden_dim * mlp_ratio)
            self.mlp = Mlp(in_features=hidden_dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop, channels_first=True)
        self.post_norm = post_norm
        self.layer_scale = layer_scale is not None

        self.infer_mode = infer_mode

        # 层缩放参数（用于稳定训练）
        if self.layer_scale:
            self.gamma1 = nn.Parameter(layer_scale * torch.ones(hidden_dim),
                                       requires_grad=True)
            self.gamma2 = nn.Parameter(layer_scale * torch.ones(hidden_dim),
                                       requires_grad=True)

    def _forward(self, x: torch.Tensor, freq_embed):
        """
        内部前向传播函数

        Args:
            x: [B, C, H, W] 输入特征图
            freq_embed: [H, W, C] 频率嵌入

        Returns:
            [B, C, H, W] 输出特征图
        """
        if not self.layer_scale:
            # 不使用层缩放的情况
            if self.post_norm:
                # Post-norm: 操作后归一化
                x = x + self.drop_path(self.norm1(self.op(x, freq_embed)))
                if self.mlp_branch:
                    x = x + self.drop_path(self.norm2(self.mlp(x)))  # FFN
            else:
                # Pre-norm: 操作前归一化
                x = x + self.drop_path(self.op(self.norm1(x), freq_embed))
                if self.mlp_branch:
                    x = x + self.drop_path(self.mlp(self.norm2(x)))  # FFN
            return x

        # 使用层缩放的情况
        if self.post_norm:
            # Post-norm + 层缩放
            x = x + self.drop_path(self.gamma1[:, None, None] * self.norm1(self.op(x, freq_embed)))
            if self.mlp_branch:
                x = x + self.drop_path(self.gamma2[:, None, None] * self.norm2(self.mlp(x)))  # FFN
        else:
            # Pre-norm + 层缩放
            x = x + self.drop_path(self.gamma1[:, None, None] * self.op(self.norm1(x), freq_embed))
            if self.mlp_branch:
                x = x + self.drop_path(self.gamma2[:, None, None] * self.mlp(self.norm2(x)))  # FFN
        return x

    def forward(self, input: torch.Tensor, freq_embed=None):
        """
        前向传播，支持梯度检查点

        Args:
            input: [B, C, H, W] 输入特征图
            freq_embed: [H, W, C] 频率嵌入

        Returns:
            [B, C, H, W] 输出特征图
        """
        if self.use_checkpoint:
            # 使用梯度检查点节省显存
            return checkpoint.checkpoint(self._forward, input, freq_embed)
        else:
            return self._forward(input, freq_embed)


class AdditionalInputSequential(nn.Sequential):
    """
    支持额外输入参数的Sequential容器
    用于传递频率嵌入等额外参数给序列中的模块
    """
    def forward(self, x, *args, **kwargs):
        """
        前向传播，将额外参数传递给序列中的每个模块（除最后一个）

        Args:
            x: 主要输入
            *args, **kwargs: 额外参数（如频率嵌入）

        Returns:
            处理后的输出
        """
        for module in self[:-1]:
            if isinstance(module, nn.Module):
                x = module(x, *args, **kwargs)
            else:
                x = module(x)
        x = self[-1](x)  # 最后一个模块（通常是下采样）不需要额外参数
        return x


class vHeat(nn.Module):
    """
    vHeat: 基于热传导方程的视觉变换器

    输入格式: [B, C, H, W] (标准图像格式)
    输出格式: [B, num_classes] (分类logits)

    架构特点:
    1. 分层设计，类似ResNet/Swin Transformer
    2. 每层使用Heat2D替代自注意力
    3. 支持多尺度特征提取
    4. 兼容标准CNN的下采样策略

    网络结构:
    - Stem层：4x下采样
    - 4个阶段，每个阶段包含多个HeatBlock
    - 每个阶段结束后进行2x下采样（除最后一个阶段）
    - 全局平均池化 + 分类头
    """
    def __init__(self, patch_size=4, in_chans=3, num_classes=1000, depths=[2, 2, 9, 2],
                 dims=[96, 192, 384, 768], drop_path_rate=0.2, patch_norm=True, post_norm=True,
                 layer_scale=None, use_checkpoint=False, mlp_ratio=4.0, img_size=224,
                 act_layer='GELU', infer_mode=False, **kwargs):
        """
        初始化vHeat模型

        Args:
            patch_size (int): Stem层的下采样倍数，默认4
            in_chans (int): 输入通道数，默认3
            num_classes (int): 分类类别数，默认1000
            depths (list): 每个阶段的块数，默认[2, 2, 9, 2]
            dims (list): 每个阶段的特征维度，默认[96, 192, 384, 768]
            drop_path_rate (float): 随机深度最大丢弃率，默认0.2
            patch_norm (bool): 是否在patch embedding后使用归一化
            post_norm (bool): 是否使用post-norm
            layer_scale: 层缩放初始值
            use_checkpoint (bool): 是否使用梯度检查点
            mlp_ratio (float): MLP扩展比例，默认4.0
            img_size (int): 输入图像尺寸，默认224
            act_layer (str): 激活函数类型
            infer_mode (bool): 推理模式
        """
        super().__init__()
        self.num_classes = num_classes
        self.num_layers = len(depths)
        # 支持单个维度自动扩展
        if isinstance(dims, int):
            dims = [int(dims * 2 ** i_layer) for i_layer in range(self.num_layers)]
        self.embed_dim = dims[0]
        self.num_features = dims[-1]
        self.dims = dims

        self.depths = depths

        # Stem层：图像预处理和初始特征提取
        self.patch_embed = StemLayer(in_chans=in_chans,
                                     out_chans=self.embed_dim,
                                     act_layer='GELU',
                                     norm_layer='LN')

        # 计算每个阶段的空间分辨率
        res0 = img_size/patch_size  # Stem层后的分辨率
        self.res = [int(res0), int(res0//2), int(res0//4), int(res0//8)]  # 每个阶段的分辨率

        # 随机深度衰减规则
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]

        self.infer_mode = infer_mode

        # 每个阶段的频率嵌入参数
        self.freq_embed = nn.ParameterList()
        for i in range(self.num_layers):
            # 为每个阶段创建对应分辨率的频率嵌入
            self.freq_embed.append(nn.Parameter(torch.zeros(self.res[i], self.res[i], self.dims[i]), requires_grad=True))
            trunc_normal_(self.freq_embed[i], std=.02)

        # 构建各个阶段的层
        self.layers = nn.ModuleList()
        for i_layer in range(self.num_layers):
            self.layers.append(self.make_layer(
                res = self.res[i_layer],  # 当前阶段的空间分辨率
                dim = self.dims[i_layer],  # 当前阶段的特征维度
                depth = depths[i_layer],  # 当前阶段的块数
                drop_path = dpr[sum(depths[:i_layer]):sum(depths[:i_layer + 1])],  # 当前阶段的随机深度率
                use_checkpoint=use_checkpoint,
                norm_layer=LayerNorm2d,
                post_norm=post_norm,
                layer_scale=layer_scale,
                # 下采样层（除最后一个阶段外）
                downsample=self.make_downsample(
                    self.dims[i_layer],
                    self.dims[i_layer + 1],
                    norm_layer=LayerNorm2d,
                ) if (i_layer < self.num_layers - 1) else nn.Identity(),
                mlp_ratio=mlp_ratio,
                infer_mode=infer_mode,
            ))

        # 分类头
        self.classifier = nn.Sequential(
            LayerNorm2d(self.num_features),  # 最终归一化
            nn.AdaptiveAvgPool2d(1),  # 全局平均池化: [B, C, H, W] -> [B, C, 1, 1]
            nn.Flatten(1),  # 展平: [B, C, 1, 1] -> [B, C]
            nn.Linear(self.num_features, num_classes),  # 分类层: [B, C] -> [B, num_classes]
        )

        # 应用权重初始化
        self.apply(self._init_weights)

    @staticmethod
    def make_downsample(dim=96, out_dim=192, norm_layer=LayerNorm2d):
        """
        创建下采样层

        Args:
            dim (int): 输入特征维度
            out_dim (int): 输出特征维度
            norm_layer: 归一化层类型

        Returns:
            nn.Sequential: 下采样模块

        架构: Conv3x3(stride=2) -> Norm
        下采样倍数: 2x
        """
        return nn.Sequential(
            # 使用3x3卷积进行2x下采样，同时改变通道数
            nn.Conv2d(dim, out_dim, kernel_size=3, stride=2, padding=1, bias=False),
            norm_layer(out_dim)  # 归一化
        )

    @staticmethod
    def make_layer(
        res=14,
        dim=96,
        depth=2,
        drop_path=[0.1, 0.1],
        use_checkpoint=False,
        norm_layer=LayerNorm2d,
        post_norm=True,
        layer_scale=None,
        downsample=nn.Identity(),
        mlp_ratio=4.0,
        infer_mode=False,
        **kwargs,
    ):
        """
        创建一个阶段的层（多个HeatBlock + 下采样）

        Args:
            res (int): 空间分辨率
            dim (int): 特征维度
            depth (int): 块的数量
            drop_path (list): 每个块的随机深度丢弃率
            use_checkpoint (bool): 是否使用梯度检查点
            norm_layer: 归一化层类型
            post_norm (bool): 是否使用post-norm
            layer_scale: 层缩放初始值
            downsample: 下采样模块
            mlp_ratio (float): MLP扩展比例
            infer_mode (bool): 推理模式

        Returns:
            AdditionalInputSequential: 包含多个HeatBlock和下采样的序列模块
        """
        assert depth == len(drop_path), "深度必须与drop_path列表长度相等"
        blocks = []
        # 创建指定数量的HeatBlock
        for d in range(depth):
            blocks.append(HeatBlock(
                res=res,
                hidden_dim=dim,
                drop_path=drop_path[d],  # 每个块使用不同的随机深度率
                norm_layer=norm_layer,
                use_checkpoint=use_checkpoint,
                mlp_ratio=mlp_ratio,
                post_norm=post_norm,
                layer_scale=layer_scale,
                infer_mode=infer_mode,
            ))

        # 返回包含所有块和下采样的序列
        return AdditionalInputSequential(
            *blocks,  # 所有HeatBlock
            downsample,  # 下采样层
        )

    def _init_weights(self, m: nn.Module):
        """
        权重初始化函数

        Args:
            m: 网络模块

        注意:
        - Conv2D层没有特殊初始化（使用PyTorch默认初始化）
        - 主要初始化Linear和LayerNorm层
        """
        if isinstance(m, nn.Linear):
            # 线性层：权重使用截断正态分布，偏置置零
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            # 层归一化：偏置置零，权重置一
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def infer_init(self):
        """
        推理模式初始化，预计算所有层的衰减权重以加速推理
        调用此方法后模型进入推理模式，不再需要频率嵌入
        """
        for i, layer in enumerate(self.layers):
            # 为每个阶段的每个HeatBlock初始化推理权重
            for block in layer[:-1]:  # 排除下采样层
                block.op.infer_init_heat2d(self.freq_embed[i])
        del self.freq_embed  # 删除频率嵌入以节省内存

    def forward_features(self, x):
        """
        特征提取前向传播（不包含分类头）

        Args:
            x: [B, C, H, W] 输入图像张量

        Returns:
            [B, C', H', W'] 特征图张量

        处理流程:
        1. Stem层处理
        2. 通过所有阶段
        3. 返回最终特征图
        """
        x = self.patch_embed(x)  # Stem层: [B, 3, 224, 224] -> [B, 96, 56, 56]

        if self.infer_mode:
            # 推理模式：使用预计算的权重
            for layer in self.layers:
                x = layer(x)
        else:
            # 训练模式：使用动态频率嵌入
            for i, layer in enumerate(self.layers):
                x = layer(x, self.freq_embed[i])  # 传递对应阶段的频率嵌入
        return x

    def forward(self, x):
        """
        完整的前向传播

        Args:
            x: [B, C, H, W] 输入图像张量

        Returns:
            [B, num_classes] 分类logits

        处理流程:
        1. 特征提取
        2. 分类头处理
        """
        x = self.forward_features(x)  # 特征提取
        x = self.classifier(x)  # 分类
        return x


if __name__ == "__main__":
    """
    测试代码：计算模型的FLOPs
    需要安装fvcore: pip install fvcore
    """
    try:
        from fvcore.nn import flop_count_table, flop_count_str, FlopCountAnalysis

        print("=" * 60)
        print("vHeat模型FLOPs分析")
        print("=" * 60)

        # 创建模型并移到GPU
        model = vHeat().cuda()
        input_tensor = torch.randn((1, 3, 224, 224), device=torch.device('cuda'))

        # 分析FLOPs
        analyze = FlopCountAnalysis(model, (input_tensor,))
        print(flop_count_str(analyze))

    except ImportError:
        print("fvcore未安装，跳过FLOPs分析")
        print("可以通过 'pip install fvcore' 安装")

        # 简单的前向传播测试
        print("\n简单前向传播测试:")
        model = vHeat()
        input_tensor = torch.randn((2, 3, 224, 224))

        print(f"输入形状: {input_tensor.shape}")
        output = model(input_tensor)
        print(f"输出形状: {output.shape}")
        print("测试通过！")



