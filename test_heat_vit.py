#!/usr/bin/env python3
"""
Test script for HeatViT model
"""

import torch
import torch.nn as nn
from vheat_vit import HeatViT, heat_vit_base, heat_vit_small, Heat2D_ViT


def test_heat2d_vit():
    """Test the Heat2D_ViT module"""
    print("Testing Heat2D_ViT module...")
    
    # Test parameters
    batch_size = 2
    seq_len = 196  # 14x14 patches
    dim = 768
    
    # Create module
    heat2d = Heat2D_ViT(dim=dim, resolution=14)
    
    # Test input
    x = torch.randn(batch_size, seq_len, dim)
    freq_embed = torch.randn(14, 14, dim)
    
    # Forward pass
    output = heat2d(x, freq_embed)
    
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    print(f"Shape preserved: {x.shape == output.shape}")
    print()


def test_heat_vit_feature_extraction():
    """Test HeatViT in feature extraction mode"""
    print("Testing HeatViT (feature extraction mode)...")
    
    # Create model for feature extraction
    model = heat_vit_base(num_classes=0)  # No classification head
    
    # Test input
    x = torch.randn(2, 3, 224, 224)
    
    # Forward pass
    features = model(x)
    
    print(f"Input shape: {x.shape}")
    print(f"Features shape: {features.shape}")
    print(f"Expected: [2, 196, 768]")
    print(f"Correct: {features.shape == torch.Size([2, 196, 768])}")
    print()


def test_heat_vit_classification():
    """Test HeatViT in classification mode"""
    print("Testing HeatViT (classification mode)...")
    
    # Create model for classification
    model = heat_vit_base(num_classes=1000)
    
    # Test input
    x = torch.randn(2, 3, 224, 224)
    
    # Forward pass
    logits = model(x)
    
    print(f"Input shape: {x.shape}")
    print(f"Logits shape: {logits.shape}")
    print(f"Expected: [2, 1000]")
    print(f"Correct: {logits.shape == torch.Size([2, 1000])}")
    print()


def test_different_sizes():
    """Test different model sizes"""
    print("Testing different model sizes...")
    
    models = {
        'small': heat_vit_small(num_classes=0),
        'base': heat_vit_base(num_classes=0),
    }
    
    x = torch.randn(1, 3, 224, 224)
    
    for name, model in models.items():
        features = model(x)
        print(f"{name}: {features.shape}")
    print()


def test_different_input_sizes():
    """Test different input image sizes"""
    print("Testing different input sizes...")
    
    input_sizes = [224, 384]
    
    for size in input_sizes:
        model = HeatViT(
            img_size=size, 
            patch_size=16, 
            embed_dim=384, 
            depth=6,
            num_classes=0
        )
        
        x = torch.randn(1, 3, size, size)
        features = model(x)
        
        expected_patches = (size // 16) ** 2
        print(f"Size {size}x{size}: {features.shape}, patches: {expected_patches}")
    print()


def test_gradient_flow():
    """Test gradient flow"""
    print("Testing gradient flow...")
    
    model = heat_vit_small(num_classes=10)
    x = torch.randn(2, 3, 224, 224, requires_grad=True)
    target = torch.randint(0, 10, (2,))
    
    # Forward pass
    logits = model(x)
    loss = nn.CrossEntropyLoss()(logits, target)
    
    # Backward pass
    loss.backward()
    
    # Check gradients
    has_grad = x.grad is not None and x.grad.abs().sum() > 0
    print(f"Loss: {loss.item():.4f}")
    print(f"Input gradients exist: {has_grad}")
    
    # Check model parameter gradients
    param_grads = []
    for name, param in model.named_parameters():
        if param.grad is not None:
            param_grads.append((name, param.grad.abs().sum().item()))
    
    print(f"Parameters with gradients: {len(param_grads)}")
    print()


def benchmark_speed():
    """Simple speed benchmark"""
    print("Speed benchmark...")
    
    model = heat_vit_base(num_classes=1000)
    model.eval()
    
    x = torch.randn(4, 3, 224, 224)
    
    # Warmup
    with torch.no_grad():
        for _ in range(10):
            _ = model(x)
    
    # Benchmark
    import time
    torch.cuda.synchronize() if torch.cuda.is_available() else None
    
    start_time = time.time()
    with torch.no_grad():
        for _ in range(100):
            _ = model(x)
    
    torch.cuda.synchronize() if torch.cuda.is_available() else None
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 100
    print(f"Average inference time: {avg_time*1000:.2f} ms")
    print()


if __name__ == "__main__":
    print("=" * 50)
    print("HeatViT Model Tests")
    print("=" * 50)
    
    try:
        test_heat2d_vit()
        test_heat_vit_feature_extraction()
        test_heat_vit_classification()
        test_different_sizes()
        test_different_input_sizes()
        test_gradient_flow()
        benchmark_speed()
        
        print("✅ All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
